 "/api/data/games/{id}/{langId}": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "Provides a list of the available games for a specified id based on the language.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "Id of the game",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "langId",
            "in": "path",
            "description": "Language Id",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "marketTypeIds",
            "in": "query",
            "description": "Markettype Ids",
            "schema": {
              "type": "array",
              "items": {
                "type": "integer",
                "format": "int64"
              }
            }
          },
          {
            "name": "onlyBetable",
            "in": "query",
            "description": "if set to `true` [only betable].",
            "schema": {
              "type": "boolean",
              "default": false
            }
          },
          {
            "name": "excludeMarketsAndOdds",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": false
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success"
          }
        }
      }
    },
    "/api/data/games/{id}/{langId}/market/{marketId}": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "Provides market information for a game, specified by id and langId, and a specified marketId.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "The identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "langId",
            "in": "path",
            "description": "The language identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "marketId",
            "in": "path",
            "description": "The market identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DTOMarket"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOMarket"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOMarket"
                }
              }
            }
          }
        }
      }
    },
    "/api/data/games/search/{pageSize}/{pageNumber}/{langId}": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "It provides a filtered list of available games.",
        "parameters": [
          {
            "name": "pageSize",
            "in": "path",
            "description": "Size of the page.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "pageNumber",
            "in": "path",
            "description": "The page number.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "langId",
            "in": "path",
            "description": "The language identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "query",
            "in": "query",
            "description": "The query.",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              }
            }
          }
        }
      }
    },
    "/api/data/games/autocomplete/search/{pageSize}/{langId}": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "Searches for games per auto completion and translates the names.",
        "parameters": [
          {
            "name": "pageSize",
            "in": "path",
            "description": "Size of the page.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "langId",
            "in": "path",
            "description": "The language identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "query",
            "in": "query",
            "description": "The query.",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success"
          }
        }
      }
    },
    "/api/data/games/{id}/extrainfo": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "Provides the extra information for a specific game.",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "The identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/DTOGameExtraInfo"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/DTOGameExtraInfo"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/DTOGameExtraInfo"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/data/games/gamesbyleague/{leagueId}/{langId}": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "Provides a list of games for a specified leagueId based on the language.",
        "parameters": [
          {
            "name": "leagueId",
            "in": "path",
            "description": "The league identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "langId",
            "in": "path",
            "description": "The language identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "pageNumber",
            "in": "query",
            "description": "The page number.",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "pageSize",
            "in": "query",
            "description": "Size of the page.",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "isTop",
            "in": "query",
            "description": "if set to `true` [is top].",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "startDate",
            "in": "query",
            "description": "The start date.",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "endDate",
            "in": "query",
            "description": "The end date.",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "marketTypeIds",
            "in": "query",
            "description": "The market type ids.",
            "schema": {
              "type": "array",
              "items": {
                "type": "integer",
                "format": "int64"
              }
            }
          },
          {
            "name": "gameTypes",
            "in": "query",
            "description": "The game types.<div class='renderedMarkdown'><br><b> DTOGameType </b><br><ul><li> 0 = NoCompetitors - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has no <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 1 = OneOnOne - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has 2 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 2 = OneOnMany - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has more than 3 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 3 = OneOnOneOnOne - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has 3 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 4 = League - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> represents a league</li></ul>",
            "schema": {
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/DTOGameType"
              }
            }
          },
          {
            "name": "gameMode",
            "in": "query",
            "description": "The game mode.",
            "schema": {
              "$ref": "#/components/schemas/DTOGameMode"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              }
            }
          }
        }
      }
    },
    "/api/data/games/{startDate}/{endDate}/{langId}": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "Provides a list of the available games for a specified language and duration.",
        "parameters": [
          {
            "name": "langId",
            "in": "path",
            "description": "The language Id for the language in which you are interested.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "startDate",
            "in": "path",
            "description": "[ISO 8601 - 'yyyy-mm-dd hh:mm:ss'] Show only events from this date/time on",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "endDate",
            "in": "path",
            "description": "[ISO 8601 - 'yyyy-mm-dd hh.mm.ss'] Show only events before this date/time on",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "pageNumber",
            "in": "query",
            "description": "The page number.",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "pageSize",
            "in": "query",
            "description": "Size of the page.",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "isTop",
            "in": "query",
            "description": "if set to `true` [is top].",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "sportId",
            "in": "query",
            "description": "It's an optional property. The sportId for the sport in which you are interested.",
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "marketTypeIds",
            "in": "query",
            "description": "It's an optional property. List of the marketTypeIds.",
            "schema": {
              "type": "array",
              "items": {
                "type": "integer",
                "format": "int64"
              }
            }
          },
          {
            "name": "gameTypes",
            "in": "query",
            "description": "GameTypes which is an enum. (NoCompetitors = 0, OneOnOne = 1, OneOnMany = 2, OneOnOneOnOne = 3, League = 4)<div class='renderedMarkdown'><br><b> DTOGameType </b><br><ul><li> 0 = NoCompetitors - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has no <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 1 = OneOnOne - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has 2 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 2 = OneOnMany - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has more than 3 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 3 = OneOnOneOnOne - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has 3 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 4 = League - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> represents a league</li></ul>",
            "schema": {
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/DTOGameType"
              }
            }
          },
          {
            "name": "gameMode",
            "in": "query",
            "description": "GameMode which is an enum. (Unknown = 0, Live = 1, Managed = 2, PreMatch = 3, Manual = 4, Premium = 5, VirtualFootball = 6, NumbersBetting = 7, VirtualBasketball = 8, VirtualTennis = 9, VirtualDogRacing = 10, VirtualHorseRacing = 11, VirtualTennisLive = 12, VirtualBaseballLive = 15, VirtualHockey = 16)",
            "schema": {
              "$ref": "#/components/schemas/DTOGameMode"
            }
          },
          {
            "name": "categoryIds",
            "in": "query",
            "description": "it's an optional property. List of the categoryIds",
            "schema": {
              "type": "array",
              "items": {
                "type": "integer",
                "format": "int64"
              }
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success"
          }
        }
      }
    },
    "/api/data/games/all/{leagueId}/{langId}": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "Provides a list of games for a specified leagueId based on the language.",
        "parameters": [
          {
            "name": "leagueId",
            "in": "path",
            "description": "The league identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "langId",
            "in": "path",
            "description": "The language identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "pageNumber",
            "in": "query",
            "description": "The page number.",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "pageSize",
            "in": "query",
            "description": "Size of the page.",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "isTop",
            "in": "query",
            "description": "if set to `true` [is top].",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "startDate",
            "in": "query",
            "description": "[ISO 8601 - 'yyyy-mm-dd hh:mm:ss'] Show only events from this date/time on",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "endDate",
            "in": "query",
            "description": "[ISO 8601 - 'yyyy-mm-dd hh:mm:ss'] Show only events before this date/time on",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "marketTypeIds",
            "in": "query",
            "description": "The market type ids.",
            "schema": {
              "type": "array",
              "items": {
                "type": "integer",
                "format": "int64"
              }
            }
          },
          {
            "name": "gameTypes",
            "in": "query",
            "description": "The game types.<div class='renderedMarkdown'><br><b> DTOGameType </b><br><ul><li> 0 = NoCompetitors - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has no <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 1 = OneOnOne - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has 2 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 2 = OneOnMany - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has more than 3 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 3 = OneOnOneOnOne - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> has 3 <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGameCompetitor\" />s</li> <li> 4 = League - <see cref=\"T:arland.bmnext.core.odds.contracts.data.DTOGame\" /> represents a league</li></ul>",
            "schema": {
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/DTOGameType"
              }
            }
          },
          {
            "name": "gameMode",
            "in": "query",
            "description": "The game mode.",
            "schema": {
              "$ref": "#/components/schemas/DTOGameMode"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOGameDTOPagedResult"
                }
              }
            }
          }
        }
      }
    },
    "/api/data/games/evaluated/{pageSize}/{offset}/{startDate}/{endDate}/{langId}": {
      "get": {
        "tags": [
          "Games"
        ],
        "summary": "Provides a list of the available evaluated games based on the pageSize, offset, language and duration.",
        "parameters": [
          {
            "name": "pageSize",
            "in": "path",
            "description": "Size of the page.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "offset",
            "in": "path",
            "description": "The offset.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "langId",
            "in": "path",
            "description": "The language identifier.",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "startDate",
            "in": "path",
            "description": "The start date.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "endDate",
            "in": "path",
            "description": "The end date.",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "sportId",
            "in": "query",
            "description": "The sport identifier.",
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "leagueId",
            "in": "query",
            "description": "The league identifier.",
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "gameCategoryId",
            "in": "query",
            "description": "The game category identifier.",
            "schema": {
              "type": "integer",
              "format": "int64"
            }
          },
          {
            "name": "orderby",
            "in": "query",
            "description": "The orderby.",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DTOAPIGameListItemDTOAPIPagedResult"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOAPIGameListItemDTOAPIPagedResult"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DTOAPIGameListItemDTOAPIPagedResult"
                }
              }
            }
          }
        }
      }