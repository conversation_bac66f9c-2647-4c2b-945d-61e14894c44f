<?php
class ArlandAPI
{
    protected static $_instance;
    private $url;
    private $endpoint;
    private $accessToken;
    private $accessTokenExpired;
    private $sessionInfo;
    private $refreshToken;
    private $tokenRefreshInterval;
    private $requestHeaders;
    public $authority;

    // Credentials which are used to get token
    private $clientIdForAuth;
    private $nonceForAuth;
    private $emailForAuth;
    private $secretKey;

    // Hashed version of the concatenated string (hashed by us after concat of clientId+Email+Nonce)
    private $signature;

    function __construct()
    {
        do_action('qm/info', 'ArlandAPI initialized');
    }

    private function prepareCredentials() {
        $this->setNonce(); // A random nonce number generated by us

        /* Prod Credentials Start */
        $this->url = 'https://nextapi.saber.systems/api';

        if ($this->authority === "SGA") {
            $this->clientIdForAuth = "con.sga.sweden.phoenix.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "BEE496D7-9926-41AE-A548-9CEB3A09D572";
        }

        if ($this->authority === "MGA") {
            $this->clientIdForAuth = "con.mga.phoenix.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "F518495B-94C8-4D15-9615-2FDB5DBD332D";
        }

        if ($this->authority === "GGA") {
            $this->clientIdForAuth = "con.gga.phoenix.opt.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "7AEBC585-F9D6-430D-AEC7-AF99BDD44D1A";
        }

        if ($this->authority === "DGA") {
            $this->clientIdForAuth = "con.dga.phoenix.opt.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "BF980F9C-7875-466F-B991-B61EEE69CDC2";
        }

        if ($this->authority === "PGA") {
            $this->clientIdForAuth = "con.pga.phoenix.opt.q.api";
            $this->emailForAuth    = "<EMAIL>";
            $this->secretKey       = "67DDFAF4-D49B-487E-B40D-C969B7FBBA82";
        }

        /* Prod Credentials End */

        if (LOCAL_ENV) {
            /* Staging Credentials Start */
            $this->url = 'https://nextapi.cleverdolphin.se/api';
            $this->clientIdForAuth = "con.mga.norway.q.api";
            $this->emailForAuth = "<EMAIL>";
            $this->secretKey = "B4BBB68C-0CAF-45E7-B5C5-6DA9893DC364";
            /* Staging Credentials End */
        }

        $this->tokenRefreshInterval = 9 * 60; // Access token will be stored for 9 min
        $this->accessToken = get_transient('px_api_matches_arland_access_token_' . $this->authority);
        $this->sessionInfo = get_transient('px_api_matches_arland_session_' . $this->authority);
        $this->accessTokenExpired = false;

        $this->generateSignature();

        // Access token check in transient
        if (($this->accessToken) || !empty($this->accessToken) || "null" != $this->accessToken) {
            $this->accessToken = unserialize($this->accessToken);
        }

        // Session token check in transient
        if (($this->sessionInfo) || !empty($this->sessionInfo) || "null" != $this->sessionInfo) {
            $this->sessionInfo = unserialize($this->sessionInfo);
        }

        if (!($this->accessToken) || empty($this->accessToken) || "null" == $this->accessToken) {
            $this->accessTokenExpired = true;
            $this->requestHeaders = [
                "ClientId: $this->clientIdForAuth",
                "Nonce: $this->nonceForAuth",
                "Signature: $this->signature",
            ];
            $this->setNonce();
            $this->generateSignature();
            $this->getAccessToken();
        } else {
            if (is_array($this->accessToken)) {
                if (array_key_exists('statusCode', $this->accessToken)) {
                    if ($this->accessToken['statusCode'] === 403) {
                        $this->accessTokenExpired = true;
                        $this->setNonce();
                        $this->generateSignature();
                        $this->requestHeaders = [
                            "ClientId: $this->clientIdForAuth",
                            "Nonce: $this->nonceForAuth",
                            "Signature: $this->signature",
                        ];
                        $this->getAccessToken();
                    } else {
                        $this->getRefreshToken();
                    }
                }
            }
        }

        if (!($this->sessionInfo) || empty($this->sessionInfo) || "null" == $this->sessionInfo) {
            $this->accessTokenExpired = true;
            $this->requestHeaders = [
                "ClientId: $this->clientIdForAuth",
                "Nonce: $this->nonceForAuth",
                "Signature: $this->signature",
            ];
            $this->getsessionInfo();
        }
    }

    public static function init() {
        $api = ArlandAPI::getInstance();
        $api->prepareCredentials();
    }

    public static function getInstance()
    {
        if (!isset(self::$_instance)) {
            self::$_instance = new ArlandAPI();
        }
        return self::$_instance;
    }

    private function setNonce()
    {
        $randomNonceString = bin2hex(random_bytes(32));

        $this->nonceForAuth = get_transient('px_api_matches_arland_auth_nonce_' . $this->authority);
        if (!($this->nonceForAuth) || empty($this->nonceForAuth) || "null" == $this->nonceForAuth) {
            set_transient('px_api_matches_arland_auth_nonce_' . $this->authority, $randomNonceString);
            $this->nonceForAuth = get_transient('px_api_matches_arland_auth_nonce_' . $this->authority);
        } else {
            if ($this->accessTokenExpired) {
                $this->nonceForAuth = $randomNonceString;
                set_transient('px_api_matches_arland_auth_nonce_' . $this->authority, $this->nonceForAuth);
            }
        }
    }

    private function generateSignature()
    {
        if (
            !empty($this->secretKey) &&
            !empty($this->emailForAuth) &&
            !empty($this->nonceForAuth) &&
            !empty($this->clientIdForAuth)
        ) {
            $this->signature = hash_hmac('sha256', $this->clientIdForAuth . $this->emailForAuth . $this->nonceForAuth, (string) $this->secretKey, true);
            $this->signature = strtoupper(bin2hex($this->signature));

            // do_action('qm/info', 'ArlandAPI->generateSignature() triggered');
        }
    }
    private function getAccessToken()
    {
        if ($this->accessTokenExpired) {
            $this->endpoint = "/auth/token";
            $curl = new Api($this->url . $this->endpoint, $this->requestHeaders);

            $response = $curl->get();
            $this->accessToken = $response;
            $this->accessTokenExpired = false;
            $this->tokenRefreshInterval = 9 * 60; // Access token will be stored for 9 min

            if(!empty($this->accessToken)) {
                set_transient('px_api_matches_arland_access_token_' . $this->authority, serialize($this->accessToken), $this->tokenRefreshInterval);
            }

            // do_action('qm/info', 'ArlandAPI->getAccessToken() triggered');
        }
    }

    private function getRefreshToken()
    {
        $token = $this->accessToken['token'];

        $customHeader = [
            "Authorization: Bearer $token",
            "Session: " . $this->sessionInfo['sessionToken']
        ];
        $customHeader = array_merge($customHeader, $this->requestHeaders);
        $this->endpoint = "/auth/refreshtoken";
        $curl = new Api($this->url . $this->endpoint, $customHeader);

        $response = $curl->put(['token' => $this->accessToken['token']]);
        $this->refreshToken = $response;
        $this->tokenRefreshInterval = 9 * 60; // Refresh token will be stored for 9 min
        set_transient('px_api_matches_arland_refresh_token_' . $this->authority, serialize($response), $this->tokenRefreshInterval);
        // do_action('qm/info', 'ArlandAPI->getRefreshToken() triggered');
    }

    private function getsessionInfo() {
        if (is_array($this->accessToken)) {
            if (array_key_exists('token', $this->accessToken)) {
                $token = $this->accessToken['token'];
                $fields = [
                    "deviceIdentifier" => "Windows",
                    "userAgent" => "Chrome",
                    "ipAddress" => "127.0.0.1",
                ];
                $fields = json_encode($fields);

                $customHeader = [
                    "Authorization: Bearer $token",
                    "Content-Length: " . strlen($fields)
                ];

                $this->endpoint = "/session/create";
                $curl = new Api($this->url . $this->endpoint, $customHeader);
                $response = $curl->post($fields);

                $this->sessionInfo = $response;
                set_transient('px_api_matches_arland_session_' . $this->authority, serialize($response), $this->tokenRefreshInterval);
            }
        }
    }

    private function getSports($languageId = "0")
    {
        if(is_string($this->accessToken)) return false;

        $token = ( !empty($this->accessToken['token']) ? $this->accessToken['token'] : '' );

        $customHeader = [
            "Authorization: Bearer $token",
            "Session: " . ( !empty($this->sessionInfo['sessionToken']) ? $this->sessionInfo['sessionToken'] : '' )
        ];

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        $this->endpoint = "/data/sports/bettable/$today/$nextMonth/$languageId";
        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        // do_action('qm/info', 'ArlandAPI->getSports() triggered');
        return $response;
    }

    private function getCategories($sportId = NULL, $languageId = "0")
    {
        if ($sportId === NULL) return;

        $token = $this->accessToken['token'];

        $customHeader = [
            "Authorization: Bearer $token",
            "Session: " . $this->sessionInfo['sessionToken']
        ];

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        // Get bettable categories
        $this->endpoint = "/data/categories/bettable/$sportId/$today/$nextMonth/$languageId";

        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        return $response;
    }

    private function getLeagues($sportId = NULL, $gameCategoryId = NULL, $languageId = "0")
    {
        if ($sportId === NULL) return;

        $token = $this->accessToken['token'];

        $customHeader = [
            "Authorization: Bearer $token",
            "Session: " . $this->sessionInfo['sessionToken']
        ];

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        // Get all leagues
        // $this->endpoint = "/data/leagues/search/$languageId?sportId=$sportId";

        // Get bettable leagues only, also only the ones in the upcoming 30 days
        $this->endpoint = "/data/leagues/bettable/$sportId/$gameCategoryId/$today/$nextMonth/$languageId";

        $curl = new Api($this->url . $this->endpoint, $customHeader);
        $response = $curl->get();

        // do_action('qm/info', 'ArlandAPI->getLeagues() triggered');

        return $response;
    }

    public static function getCompetitors($gameID)
    {
        $api = ArlandAPI::getInstance();

        $token = $api->accessToken['token'];

        $customHeader = [
            "Authorization: Bearer $token",
            "Session: " . $api->sessionInfo['sessionToken']
        ];

        // Get game competitors
        $api->endpoint = "/data/gamecompetitors?gameIds=" . $gameID;
        $curl = new Api($api->url . $api->endpoint, $customHeader);
        $response = $curl->get();

        if ($response) {
            return [
                'home' => $response[0]['name'],
                'away' => $response[1]['name']
            ];
        }

        return $response;
    }

    public static function getMatches()
    {
        $api = ArlandAPI::getInstance();
        $api->fetch();
    }


    public static function getDateForTodayAndNextMonth()
    {
        $today = date('Y-m-d');
        $add_months = 1;
        $last_day_of_month = date("Y-m-t", strtotime($today));

        if ($today == $last_day_of_month) {

            //Need to use a some custom logic, because +1 months will fail if the start date is equal to the last day of the month...
            //Start calculation from first day of this month, then get the last day of next month(s)
            $today = date('Y-m-d', strtotime("first day of this month", strtotime($today)));

            // note the Y-m-t --not-- Y-m-d
            // t is total number of days in month
            $next_month = date('Y-m-t', strtotime("+" . $add_months . " months", strtotime($today)));
        } else {
            //The start date isn't the last day of the month, we can use the normal +months functionality
            $next_month = date('Y-m-d', strtotime("+" . $add_months . " months", strtotime($today)));
        }

        return [
            'today' => $today . 'T00:00:00Z',
            'next_month' =>  $next_month . 'T00:00:00Z'
        ];
    }

    /**
     * Optimized method to fetch all games by date range with sport filtering
     * This reduces API calls by fetching games directly instead of going through sports->categories->leagues->games
     */
    private function getGamesByDateRange($languageId = "0", $sportId = null, $pageSize = 100, $pageNumber = 1)
    {
        if(is_string($this->accessToken)) return false;

        $token = !empty($this->accessToken['token']) ? $this->accessToken['token'] : '';

        $customHeader = [
            "Authorization: Bearer $token",
            "Session: " . (!empty($this->sessionInfo['sessionToken']) ? $this->sessionInfo['sessionToken'] : '')
        ];

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        // Build endpoint with optional sport filter
        $this->endpoint = "/data/games/$today/$nextMonth/$languageId?pageSize=$pageSize&pageNumber=$pageNumber&gameTypes=1&marketTypeIds=1";

        if ($sportId !== null) {
            $this->endpoint .= "&sportId=$sportId";
        }

        $fullUrl = $this->url . $this->endpoint;

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::getGamesByDateRange - Request',
                'URL' => $fullUrl,
                'Headers' => $customHeader,
                'Parameters' => [
                    'languageId' => $languageId,
                    'sportId' => $sportId,
                    'pageSize' => $pageSize,
                    'pageNumber' => $pageNumber,
                    'dateRange' => "$today to $nextMonth"
                ]
            ]);
        }

        $curl = new Api($fullUrl, $customHeader);
        $response = $curl->get();

        if (DEBUG_MODE) {
            $responseSize = is_array($response) ? count($response) : (is_string($response) ? strlen($response) : 0);
            $itemCount = (is_array($response) && isset($response['items'])) ? count($response['items']) : 0;

            do_action('qm/debug', [
                'ArlandAPI::getGamesByDateRange - Response',
                'Response Size' => $responseSize,
                'Items Count' => $itemCount,
                'Response Type' => gettype($response),
                'Has Items' => isset($response['items']) ? 'Yes' : 'No'
            ]);

            if (is_array($response) && isset($response['totalCount'])) {
                do_action('qm/debug', [
                    'ArlandAPI::getGamesByDateRange - Pagination Info',
                    'Total Count' => $response['totalCount'],
                    'Current Page' => $pageNumber,
                    'Page Size' => $pageSize,
                    'Items in Response' => $itemCount
                ]);
            }
        }

        return $response;
    }

    /**
     * Optimized method to fetch games using the search endpoint
     * This allows for more flexible filtering and pagination
     */
    private function searchGames($languageId = "0", $pageSize = 100, $pageNumber = 1, $query = "")
    {
        if(is_string($this->accessToken)) return false;

        $token = !empty($this->accessToken['token']) ? $this->accessToken['token'] : '';

        $customHeader = [
            "Authorization: Bearer $token",
            "Session: " . (!empty($this->sessionInfo['sessionToken']) ? $this->sessionInfo['sessionToken'] : '')
        ];

        $this->endpoint = "/data/games/search/$pageSize/$pageNumber/$languageId";

        if (!empty($query)) {
            $this->endpoint .= "?query=" . urlencode($query);
        }

        $fullUrl = $this->url . $this->endpoint;

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::searchGames - Request',
                'URL' => $fullUrl,
                'Headers' => $customHeader,
                'Parameters' => [
                    'languageId' => $languageId,
                    'pageSize' => $pageSize,
                    'pageNumber' => $pageNumber,
                    'query' => $query
                ]
            ]);
        }

        $curl = new Api($fullUrl, $customHeader);
        $response = $curl->get();

        if (DEBUG_MODE) {
            $responseSize = is_array($response) ? count($response) : (is_string($response) ? strlen($response) : 0);
            $itemCount = (is_array($response) && isset($response['items'])) ? count($response['items']) : 0;

            do_action('qm/debug', [
                'ArlandAPI::searchGames - Response',
                'Response Size' => $responseSize,
                'Items Count' => $itemCount,
                'Response Type' => gettype($response),
                'Query Used' => $query
            ]);
        }

        return $response;
    }

    /**
     * Optimized method to fetch evaluated games with sport and league filtering
     * This is the most efficient endpoint for getting games with hierarchical data
     */
    private function getEvaluatedGames($languageId = "0", $pageSize = 100, $offset = 0, $sportId = null, $leagueId = null)
    {
        if(is_string($this->accessToken)) return false;

        $token = !empty($this->accessToken['token']) ? $this->accessToken['token'] : '';

        $customHeader = [
            "Authorization: Bearer $token",
            "Session: " . (!empty($this->sessionInfo['sessionToken']) ? $this->sessionInfo['sessionToken'] : '')
        ];

        $dates = static::getDateForTodayAndNextMonth();
        $today = $dates['today'];
        $nextMonth = $dates['next_month'];

        $this->endpoint = "/data/games/evaluated/$pageSize/$offset/$today/$nextMonth/$languageId";

        $queryParams = [];
        if ($sportId !== null) {
            $queryParams[] = "sportId=$sportId";
        }
        if ($leagueId !== null) {
            $queryParams[] = "leagueId=$leagueId";
        }

        if (!empty($queryParams)) {
            $this->endpoint .= "?" . implode("&", $queryParams);
        }

        $fullUrl = $this->url . $this->endpoint;

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::getEvaluatedGames - Request',
                'URL' => $fullUrl,
                'Headers' => $customHeader,
                'Parameters' => [
                    'languageId' => $languageId,
                    'pageSize' => $pageSize,
                    'offset' => $offset,
                    'sportId' => $sportId,
                    'leagueId' => $leagueId,
                    'dateRange' => "$today to $nextMonth"
                ]
            ]);
        }

        $curl = new Api($fullUrl, $customHeader);
        $response = $curl->get();

        if (DEBUG_MODE) {
            $responseSize = is_array($response) ? count($response) : (is_string($response) ? strlen($response) : 0);
            $itemCount = (is_array($response) && isset($response['items'])) ? count($response['items']) : 0;

            do_action('qm/debug', [
                'ArlandAPI::getEvaluatedGames - Response',
                'Response Size' => $responseSize,
                'Items Count' => $itemCount,
                'Response Type' => gettype($response),
                'Sport Filter' => $sportId ?? 'None',
                'League Filter' => $leagueId ?? 'None'
            ]);
        }

        return $response;
    }

    /**
     * Optimized fetch method that uses fewer API calls
     * Uses the games by date range endpoint to get all games at once, then organizes by sport/league
     */
    public static function fetchOptimized($languageId = "0")
    {
        $startTime = microtime(true);
        $api = ArlandAPI::getInstance();
        $matchesTransient = [];

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchOptimized - Started',
                'Language ID' => $languageId,
                'Authority' => $api->authority,
                'Start Time' => date('Y-m-d H:i:s')
            ]);
        }

        // First, get all sports to have the mapping
        $sports = $api->getSports($languageId);
        if (!is_array($sports)) {
            if (DEBUG_MODE) {
                do_action('qm/debug', [
                    'ArlandAPI::fetchOptimized - Error',
                    'Message' => 'Failed to fetch sports or invalid response',
                    'Sports Response Type' => gettype($sports)
                ]);
            }
            return false;
        }

        $sportsMap = [];
        foreach ($sports as $sport) {
            if (!empty($sport['id'])) {
                $sportsMap[$sport['id']] = $sport['name'];
            }
        }

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchOptimized - Sports Loaded',
                'Sports Count' => count($sportsMap),
                'Sports' => array_values($sportsMap)
            ]);
        }

        // Fetch all games at once using the optimized endpoint
        $pageSize = 100;
        $pageNumber = 1;
        $allGames = [];
        $totalApiCalls = 1; // Count the getSports call

        do {
            $gamesResponse = $api->getGamesByDateRange($languageId, null, $pageSize, $pageNumber);
            $totalApiCalls++;

            if (is_array($gamesResponse) && !empty($gamesResponse['items'])) {
                $allGames = array_merge($allGames, $gamesResponse['items']);
                $hasMorePages = count($gamesResponse['items']) === $pageSize;
                $pageNumber++;

                if (DEBUG_MODE) {
                    do_action('qm/debug', [
                        'ArlandAPI::fetchOptimized - Page Loaded',
                        'Page Number' => $pageNumber - 1,
                        'Items in Page' => count($gamesResponse['items']),
                        'Total Games So Far' => count($allGames),
                        'Has More Pages' => $hasMorePages ? 'Yes' : 'No'
                    ]);
                }
            } else {
                $hasMorePages = false;
                if (DEBUG_MODE) {
                    do_action('qm/debug', [
                        'ArlandAPI::fetchOptimized - No More Pages',
                        'Last Response Type' => gettype($gamesResponse),
                        'Last Response Empty' => empty($gamesResponse) ? 'Yes' : 'No'
                    ]);
                }
            }
        } while ($hasMorePages);

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchOptimized - Games Fetched',
                'Total Games' => count($allGames),
                'Total API Calls' => $totalApiCalls,
                'Processing Started' => date('Y-m-d H:i:s')
            ]);
        }

        // Process games and organize by sport/league
        $processedGames = 0;
        $skippedGames = 0;
        $gamesWithoutMarkets = 0;

        foreach ($allGames as $game) {
            if (empty($game['sportId']) || empty($game['leagueId'])) {
                $skippedGames++;
                continue;
            }

            $sportId = $game['sportId'];
            $leagueId = $game['leagueId'];
            $gameId = $game['id'];

            // Skip if sport is not in our sports map
            if (!isset($sportsMap[$sportId])) {
                $skippedGames++;
                continue;
            }

            // Find 1x2 market
            $market = [];
            if (!empty($game['markets'])) {
                foreach ($game['markets'] as $marketData) {
                    if ($marketData['marketTypeId'] == "1") {
                        $market = $marketData;
                        break;
                    }
                }
            }

            // Skip if no 1x2 market or incomplete odds
            if (empty($market) || empty($market['odds']) || count($market['odds']) < 3) {
                $gamesWithoutMarkets++;
                continue;
            }

            if (
                !($market['odds'][0]['value']) ||
                !($market['odds'][1]['value']) ||
                !($market['odds'][2]['value']) ||
                empty($market['odds'][0]['value']) ||
                empty($market['odds'][1]['value']) ||
                empty($market['odds'][2]['value'])
            ) {
                $gamesWithoutMarkets++;
                continue;
            }

            // Initialize sport structure if not exists
            if (!array_key_exists($sportId, $matchesTransient)) {
                $matchesTransient[$sportId] = [
                    "SportName" => $sportsMap[$sportId],
                    "SportID" => $sportId
                ];
            }

            // Initialize league structure if not exists
            if (!array_key_exists($leagueId, $matchesTransient[$sportId])) {
                $matchesTransient[$sportId][$leagueId] = [
                    'LeagueName' => $game['leagueName'] ?? 'Unknown League'
                ];
            }

            // Parse competitors from game name
            $splittedGameName = explode('-', (string) $game['name']);
            $competitors = [
                'home' => (!empty($splittedGameName[0]) ? trim($splittedGameName[0]) : ''),
                'away' => (!empty($splittedGameName[1]) ? trim($splittedGameName[1]) : '')
            ];

            // Add game data
            $matchesTransient[$sportId][$leagueId][$gameId] = [
                'SportID'  => $sportId,
                'LeagueID' => $leagueId,
                'GameID'   => $gameId,
                'GameDate' => $game['startDate'],
                'Home'     => $competitors['home'],
                'Away'     => $competitors['away'],
                'LineIDs'  => [
                    'Home' => $market['odds'][0]['id'],
                    'Draw' => $market['odds'][1]['id'],
                    'Away' => $market['odds'][2]['id']
                ],
                'Odds' => [
                    'Home' => $market['odds'][0]['value'],
                    'Draw' => $market['odds'][1]['value'],
                    'Away' => $market['odds'][2]['value'],
                ]
            ];

            $processedGames++;
        }

        // Clean up empty leagues and sports
        foreach ($matchesTransient as $sportId => $sportData) {
            if (is_array($sportData)) {
                foreach ($sportData as $leagueId => $leagueData) {
                    if (is_array($leagueData) && count($leagueData) === 1 && isset($leagueData['LeagueName'])) {
                        unset($matchesTransient[$sportId][$leagueId]);
                    }
                }

                if (count($matchesTransient[$sportId]) === 2 && isset($matchesTransient[$sportId]['SportName']) && isset($matchesTransient[$sportId]['SportID'])) {
                    unset($matchesTransient[$sportId]);
                }
            }
        }

        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchOptimized - Processing Complete',
                'Total Games Fetched' => count($allGames),
                'Games Processed' => $processedGames,
                'Games Skipped' => $skippedGames,
                'Games Without Markets' => $gamesWithoutMarkets,
                'Sports with Games' => count($matchesTransient),
                'Total API Calls' => $totalApiCalls,
                'Execution Time' => $executionTime . ' seconds'
            ]);
        }

        $matchesTransient[] = ['lastUpdatedAt' => (new \DateTime())->format('Y-m-d H:i:s')];
        set_transient('px_api_matches_arland_' . $api->authority, $matchesTransient);

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchOptimized - Completed',
                'Transient Key' => 'px_api_matches_arland_' . $api->authority,
                'Final Data Structure' => array_keys($matchesTransient),
                'End Time' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * Optimized method to fetch games for a specific sport
     * This is useful when you only need data for one sport
     */
    public static function fetchBySport($sportId, $languageId = "0")
    {
        $startTime = microtime(true);
        $api = ArlandAPI::getInstance();
        $matchesTransient = [];

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchBySport - Started',
                'Sport ID' => $sportId,
                'Language ID' => $languageId,
                'Authority' => $api->authority,
                'Start Time' => date('Y-m-d H:i:s')
            ]);
        }

        // Get sport name
        $sports = $api->getSports($languageId);
        if (!is_array($sports)) {
            if (DEBUG_MODE) {
                do_action('qm/debug', [
                    'ArlandAPI::fetchBySport - Error',
                    'Message' => 'Failed to fetch sports or invalid response',
                    'Sports Response Type' => gettype($sports)
                ]);
            }
            return false;
        }

        $sportName = '';
        foreach ($sports as $sport) {
            if ($sport['id'] == $sportId) {
                $sportName = $sport['name'];
                break;
            }
        }

        if (empty($sportName)) {
            if (DEBUG_MODE) {
                do_action('qm/debug', [
                    'ArlandAPI::fetchBySport - Error',
                    'Message' => 'Sport not found',
                    'Requested Sport ID' => $sportId,
                    'Available Sports' => array_column($sports, 'name', 'id')
                ]);
            }
            return false;
        }

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchBySport - Sport Found',
                'Sport Name' => $sportName,
                'Sport ID' => $sportId
            ]);
        }

        // Fetch games for this specific sport
        $pageSize = 100;
        $pageNumber = 1;
        $allGames = [];
        $totalApiCalls = 1; // Count the getSports call

        do {
            $gamesResponse = $api->getGamesByDateRange($languageId, $sportId, $pageSize, $pageNumber);
            $totalApiCalls++;

            if (is_array($gamesResponse) && !empty($gamesResponse['items'])) {
                $allGames = array_merge($allGames, $gamesResponse['items']);
                $hasMorePages = count($gamesResponse['items']) === $pageSize;
                $pageNumber++;

                if (DEBUG_MODE) {
                    do_action('qm/debug', [
                        'ArlandAPI::fetchBySport - Page Loaded',
                        'Page Number' => $pageNumber - 1,
                        'Items in Page' => count($gamesResponse['items']),
                        'Total Games So Far' => count($allGames)
                    ]);
                }
            } else {
                $hasMorePages = false;
            }
        } while ($hasMorePages);

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchBySport - Games Fetched',
                'Total Games' => count($allGames),
                'Total API Calls' => $totalApiCalls
            ]);
        }

        // Initialize sport structure
        $matchesTransient[$sportId] = [
            "SportName" => $sportName,
            "SportID" => $sportId
        ];

        // Process games and organize by league
        $processedGames = 0;
        $skippedGames = 0;
        $gamesWithoutMarkets = 0;

        foreach ($allGames as $game) {
            if (empty($game['leagueId'])) {
                $skippedGames++;
                continue;
            }

            $leagueId = $game['leagueId'];
            $gameId = $game['id'];

            // Find 1x2 market
            $market = [];
            if (!empty($game['markets'])) {
                foreach ($game['markets'] as $marketData) {
                    if ($marketData['marketTypeId'] == "1") {
                        $market = $marketData;
                        break;
                    }
                }
            }

            // Skip if no 1x2 market or incomplete odds
            if (empty($market) || empty($market['odds']) || count($market['odds']) < 3) {
                $gamesWithoutMarkets++;
                continue;
            }

            if (
                !($market['odds'][0]['value']) ||
                !($market['odds'][1]['value']) ||
                !($market['odds'][2]['value']) ||
                empty($market['odds'][0]['value']) ||
                empty($market['odds'][1]['value']) ||
                empty($market['odds'][2]['value'])
            ) {
                $gamesWithoutMarkets++;
                continue;
            }

            // Initialize league structure if not exists
            if (!array_key_exists($leagueId, $matchesTransient[$sportId])) {
                $matchesTransient[$sportId][$leagueId] = [
                    'LeagueName' => $game['leagueName'] ?? 'Unknown League'
                ];
            }

            // Parse competitors from game name
            $splittedGameName = explode('-', (string) $game['name']);
            $competitors = [
                'home' => (!empty($splittedGameName[0]) ? trim($splittedGameName[0]) : ''),
                'away' => (!empty($splittedGameName[1]) ? trim($splittedGameName[1]) : '')
            ];

            // Add game data
            $matchesTransient[$sportId][$leagueId][$gameId] = [
                'SportID'  => $sportId,
                'LeagueID' => $leagueId,
                'GameID'   => $gameId,
                'GameDate' => $game['startDate'],
                'Home'     => $competitors['home'],
                'Away'     => $competitors['away'],
                'LineIDs'  => [
                    'Home' => $market['odds'][0]['id'],
                    'Draw' => $market['odds'][1]['id'],
                    'Away' => $market['odds'][2]['id']
                ],
                'Odds' => [
                    'Home' => $market['odds'][0]['value'],
                    'Draw' => $market['odds'][1]['value'],
                    'Away' => $market['odds'][2]['value'],
                ]
            ];

            $processedGames++;
        }

        // Clean up empty leagues
        foreach ($matchesTransient[$sportId] as $leagueId => $leagueData) {
            if (is_array($leagueData) && count($leagueData) === 1 && isset($leagueData['LeagueName'])) {
                unset($matchesTransient[$sportId][$leagueId]);
            }
        }

        // If sport has no games, remove it
        if (count($matchesTransient[$sportId]) === 2 && isset($matchesTransient[$sportId]['SportName']) && isset($matchesTransient[$sportId]['SportID'])) {
            unset($matchesTransient[$sportId]);
        }

        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchBySport - Processing Complete',
                'Sport' => $sportName,
                'Total Games Fetched' => count($allGames),
                'Games Processed' => $processedGames,
                'Games Skipped' => $skippedGames,
                'Games Without Markets' => $gamesWithoutMarkets,
                'Leagues Found' => count($matchesTransient[$sportId]) - 2, // Subtract SportName and SportID
                'Total API Calls' => $totalApiCalls,
                'Execution Time' => $executionTime . ' seconds'
            ]);
        }

        $matchesTransient[] = ['lastUpdatedAt' => (new \DateTime())->format('Y-m-d H:i:s')];
        $transientKey = 'px_api_matches_arland_sport_' . $sportId . '_' . $api->authority;
        set_transient($transientKey, $matchesTransient);

        if (DEBUG_MODE) {
            do_action('qm/debug', [
                'ArlandAPI::fetchBySport - Completed',
                'Transient Key' => $transientKey,
                'End Time' => date('Y-m-d H:i:s')
            ]);
        }

        return $matchesTransient;
    }

    /**
     * Legacy fetch method - kept for backward compatibility
     */
    public static function fetch($languageId = "0")
    {
        // Use the optimized method by default
        return self::fetchOptimized($languageId);
    }
}